import type { TreeNode } from '../types/file-types';

interface TreeDiffResult {
  added: Set<string>;
  removed: Set<string>;
  modified: Set<string>;
  unchanged: Set<string>;
}

export function diffTrees(oldTree: TreeNode[], newTree: TreeNode[]): TreeDiffResult {
  const result: TreeDiffResult = {
    added: new Set(),
    removed: new Set(),
    modified: new Set(),
    unchanged: new Set()
  };

  const oldPaths = new Map<string, TreeNode>();
  const newPaths = new Map<string, TreeNode>();

  function collectPaths(nodes: TreeNode[], map: Map<string, TreeNode>) {
    for (const node of nodes) {
      map.set(node.path, node);
      if (node.type === 'directory' && node.children) {
        collectPaths(node.children, map);
      }
    }
  }

  collectPaths(oldTree, oldPaths);
  collectPaths(newTree, newPaths);

  for (const [path, newNode] of newPaths) {
    const oldNode = oldPaths.get(path);
    if (!oldNode) {
      result.added.add(path);
    } else if (hasNodeChanged(oldNode, newNode)) {
      result.modified.add(path);
    } else {
      result.unchanged.add(path);
    }
  }

  for (const path of oldPaths.keys()) {
    if (!newPaths.has(path)) {
      result.removed.add(path);
    }
  }

  return result;
}

function hasNodeChanged(oldNode: TreeNode, newNode: TreeNode): boolean {
  if (oldNode.type !== newNode.type) return true;
  if (oldNode.name !== newNode.name) return true;
  
  if (oldNode.type === 'file' && newNode.type === 'file') {
    const oldData = oldNode.fileData;
    const newData = newNode.fileData;
    
    if (!oldData && !newData) return false;
    if (!oldData || !newData) return true;
    
    return (
      oldData.size !== newData.size ||
      oldData.tokenCount !== newData.tokenCount ||
      oldData.isContentLoaded !== newData.isContentLoaded
    );
  }
  
  return false;
}

export function applyTreeDiff(
  currentTree: TreeNode[],
  newTree: TreeNode[],
  diff: TreeDiffResult
): TreeNode[] {
  if (diff.added.size === 0 && diff.removed.size === 0 && diff.modified.size === 0) {
    return currentTree;
  }
  
  function mergeNodes(current: TreeNode[], updates: TreeNode[]): TreeNode[] {
    const merged: TreeNode[] = [];
    const updateMap = new Map<string, TreeNode>();
    
    for (const node of updates) {
      updateMap.set(node.path, node);
    }
    
    for (const node of current) {
      if (diff.removed.has(node.path)) {
        continue;
      }
      
      const updatedNode = updateMap.get(node.path);
      if (updatedNode) {
        if (diff.modified.has(node.path)) {
          merged.push({
            ...updatedNode,
            isExpanded: node.isExpanded
          });
        } else {
          const mergedNode = { ...node };
          if (node.type === 'directory' && node.children && updatedNode.children) {
            mergedNode.children = mergeNodes(node.children, updatedNode.children);
          }
          merged.push(mergedNode);
        }
        updateMap.delete(node.path);
      } else {
        merged.push(node);
      }
    }
    
    for (const [, node] of updateMap) {
      if (diff.added.has(node.path)) {
        merged.push(node);
      }
    }
    
    return merged;
  }
  
  return mergeNodes(currentTree, newTree);
}