export class AdaptiveBatchProcessor {
  private batchSize: number;
  private minBatchSize: number;
  private maxBatchSize: number;
  private targetFrameTime: number;
  private lastProcessTime: number = 0;
  private performanceHistory: number[] = [];
  private historySize: number = 5;

  constructor(
    initialBatchSize = 50,
    minBatchSize = 10,
    maxBatchSize = 500,
    targetFrameTime = 16 // 60fps target
  ) {
    this.batchSize = initialBatchSize;
    this.minBatchSize = minBatchSize;
    this.maxBatchSize = maxBatchSize;
    this.targetFrameTime = targetFrameTime;
  }

  startBatch(): number {
    this.lastProcessTime = performance.now();
    return this.batchSize;
  }

  endBatch(): void {
    const processingTime = performance.now() - this.lastProcessTime;
    
    // Add to performance history
    this.performanceHistory.push(processingTime);
    if (this.performanceHistory.length > this.historySize) {
      this.performanceHistory.shift();
    }

    // Calculate average processing time
    const avgTime = this.performanceHistory.reduce((a, b) => a + b, 0) / this.performanceHistory.length;

    // Adjust batch size based on performance
    if (avgTime < this.targetFrameTime * 0.5) {
      // Processing is fast, increase batch size
      this.batchSize = Math.min(
        Math.floor(this.batchSize * 1.5),
        this.maxBatchSize
      );
    } else if (avgTime > this.targetFrameTime) {
      // Processing is slow, decrease batch size
      this.batchSize = Math.max(
        Math.floor(this.batchSize * 0.7),
        this.minBatchSize
      );
    } else if (avgTime < this.targetFrameTime * 0.8) {
      // Processing is good but could be better, slightly increase
      this.batchSize = Math.min(
        Math.floor(this.batchSize * 1.1),
        this.maxBatchSize
      );
    }
  }

  getCurrentBatchSize(): number {
    return this.batchSize;
  }

  reset(): void {
    this.performanceHistory = [];
    this.batchSize = 50; // Reset to initial size
  }

  getStats(): {
    currentBatchSize: number;
    avgProcessingTime: number;
    targetTime: number;
  } {
    const avgTime = this.performanceHistory.length > 0
      ? this.performanceHistory.reduce((a, b) => a + b, 0) / this.performanceHistory.length
      : 0;

    return {
      currentBatchSize: this.batchSize,
      avgProcessingTime: avgTime,
      targetTime: this.targetFrameTime
    };
  }
}