export class ProgressThrottle {
  private lastReportedProgress = 0;
  private lastUpdateTime = 0;
  private readonly minProgressIncrement: number;
  private readonly minTimeInterval: number;
  private pendingUpdate: number | null = null;
  private timeoutId: NodeJS.Timeout | null = null;

  constructor(minProgressIncrement = 5, minTimeInterval = 100) {
    this.minProgressIncrement = minProgressIncrement;
    this.minTimeInterval = minTimeInterval;
  }

  shouldUpdate(progress: number): boolean {
    const now = Date.now();
    const timeSinceLastUpdate = now - this.lastUpdateTime;
    const progressDelta = Math.abs(progress - this.lastReportedProgress);

    // Always update on 0% or 100%
    if (progress === 0 || progress === 100) {
      this.lastReportedProgress = progress;
      this.lastUpdateTime = now;
      return true;
    }

    // Update if we've made significant progress OR enough time has passed
    if (progressDelta >= this.minProgressIncrement || 
        timeSinceLastUpdate >= this.minTimeInterval * 2) {
      this.lastReportedProgress = progress;
      this.lastUpdateTime = now;
      return true;
    }

    // Store pending update for later
    this.pendingUpdate = progress;
    this.schedulePendingUpdate();
    return false;
  }

  private schedulePendingUpdate(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }

    this.timeoutId = setTimeout(() => {
      if (this.pendingUpdate !== null) {
        this.lastReportedProgress = this.pendingUpdate;
        this.lastUpdateTime = Date.now();
        this.pendingUpdate = null;
      }
      this.timeoutId = null;
    }, this.minTimeInterval);
  }

  reset(): void {
    this.lastReportedProgress = 0;
    this.lastUpdateTime = 0;
    this.pendingUpdate = null;
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }

  cleanup(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }
}